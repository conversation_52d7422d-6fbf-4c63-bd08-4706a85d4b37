"""
FastMCP Server for Claude Code SDK Integration
"""
import os
from typing import Optional, List, Dict, Any
from pathlib import Path
from fastmcp import FastMCP, Context
from claude_code_sdk import query, ClaudeCodeOptions

# Initialize FastMCP server
mcp = FastMCP("Claude Code SDK Server 🤖")


@mcp.tool
async def execute_sub_task(
    prompt: str,
    max_turns: int = 3,
    system_prompt: Optional[str] = None,
    cwd: Optional[str] = None,
    allowed_tools: Optional[List[str]] = None,
    permission_mode: str = "acceptEdits",
    ctx: Context = None
) -> Dict[str, Any]:
    """
    Execute a sub-task using Claude Code SDK.
    
    Args:
        prompt: The task prompt to execute
        max_turns: Maximum number of conversation turns (default: 3)
        system_prompt: Custom system prompt (default: "You are a helpful assistant")
        cwd: Working directory path (default: current directory)
        allowed_tools: List of allowed tools (default: ["Read", "Write", "Bash"])
        permission_mode: Permission mode for edits (default: "acceptEdits")
        ctx: MCP context for logging
    
    Returns:
        Dictionary containing the execution results and messages
    """
    
    # Log the request
    if ctx:
        await ctx.info(f"Executing sub-task: {prompt[:100]}...")
    
    # Set default values
    if system_prompt is None:
        system_prompt = "You are a helpful assistant"
    
    if cwd is None:
        cwd = os.getcwd()
    
    if allowed_tools is None:
        allowed_tools = ["Read", "Write", "Bash"]
    
    # Convert cwd to Path object
    cwd_path = Path(cwd)
    
    # Validate working directory
    if not cwd_path.exists():
        error_msg = f"Working directory does not exist: {cwd}"
        if ctx:
            await ctx.error(error_msg)
        return {
            "success": False,
            "error": error_msg,
            "prompt": prompt
        }
    
    # Create options
    options = ClaudeCodeOptions(
        max_turns=max_turns,
        system_prompt=system_prompt,
        cwd=cwd_path,
        allowed_tools=allowed_tools,
        permission_mode=permission_mode
    )
    
    try:
        # Execute the query and collect all messages
        messages = []
        
        if ctx:
            await ctx.info("Starting Claude Code SDK execution...")
        
        async for message in query(prompt=prompt, options=options):
            messages.append(str(message))
            if ctx:
                await ctx.info(f"Received message: {str(message)[:200]}...")
        
        if ctx:
            await ctx.info(f"Successfully completed sub-task with {len(messages)} messages")
        
        return {
            "success": True,
            "messages": messages,
            "prompt": prompt,
            "options": {
                "max_turns": max_turns,
                "system_prompt": system_prompt,
                "cwd": str(cwd_path),
                "allowed_tools": allowed_tools,
                "permission_mode": permission_mode
            },
            "metadata": {
                "total_messages": len(messages),
                "working_directory": str(cwd_path)
            }
        }
        
    except Exception as e:
        error_msg = f"Error executing sub-task: {str(e)}"
        if ctx:
            await ctx.error(error_msg)
        
        return {
            "success": False,
            "error": error_msg,
            "prompt": prompt,
            "options": {
                "max_turns": max_turns,
                "system_prompt": system_prompt,
                "cwd": str(cwd_path) if 'cwd_path' in locals() else cwd,
                "allowed_tools": allowed_tools,
                "permission_mode": permission_mode
            }
        }


@mcp.tool
def get_available_tools() -> List[str]:
    """
    Get the list of available tools for Claude Code SDK.
    
    Returns:
        List of available tool names
    """
    return ["Read", "Write", "Bash", "Search", "Replace"]


@mcp.tool
def get_permission_modes() -> List[Dict[str, str]]:
    """
    Get the available permission modes for Claude Code SDK.
    
    Returns:
        List of permission modes with descriptions
    """
    return [
        {
            "mode": "acceptEdits",
            "description": "Automatically accept all edit operations"
        },
        {
            "mode": "confirmEdits", 
            "description": "Require confirmation for edit operations"
        },
        {
            "mode": "rejectEdits",
            "description": "Reject all edit operations"
        }
    ]


@mcp.prompt
def create_coding_prompt(
    task_description: str,
    language: Optional[str] = None,
    framework: Optional[str] = None,
    requirements: Optional[str] = None
) -> str:
    """
    Generate a well-formatted coding prompt for Claude Code SDK.
    
    Args:
        task_description: Main description of the coding task
        language: Programming language (e.g., "Python", "JavaScript", "Rust")
        framework: Framework or library (e.g., "React", "FastAPI", "Django")
        requirements: Additional requirements or constraints
    
    Returns:
        A well-formatted prompt string for coding tasks
    """
    parts = [task_description]
    
    if language:
        parts.append(f"Use {language} programming language.")
    
    if framework:
        parts.append(f"Use the {framework} framework.")
    
    if requirements:
        parts.append(f"Additional requirements: {requirements}")
    
    return " ".join(parts)


if __name__ == "__main__":
    print("🚀 Starting Claude Code SDK MCP Server...")
    print("   Available tools:")
    print("   - execute_sub_task: Execute coding tasks using Claude Code SDK")
    print("   - get_available_tools: List available tools for Claude Code SDK")
    print("   - get_permission_modes: List available permission modes")
    print("   Available prompts:")
    print("   - create_coding_prompt: Generate well-formatted coding prompts")
    print()
    
    # Run the server
    mcp.run()
