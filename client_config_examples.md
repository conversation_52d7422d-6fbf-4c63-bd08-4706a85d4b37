# 客户端配置示例 🔧

本文档介绍如何在不同的MCP客户端中使用 Fal.ai Image Generation Server。

## 1. <PERSON> 配置

### macOS
编辑配置文件 `~/Library/Application Support/Claude/claude_desktop_config.json`:

```json
{
  "mcpServers": {
    "fal-images": {
      "command": "python",
      "args": ["/Users/<USER>/Documents/github/fastmcptest/fal_image_server.py"],
      "env": {
        "FAL_KEY": "your-fal-api-key-here"
      }
    }
  }
}
```

### Windows
编辑配置文件 `%APPDATA%\Claude\claude_desktop_config.json`:

```json
{
  "mcpServers": {
    "fal-images": {
      "command": "python",
      "args": ["C:\\path\\to\\fal_image_server.py"],
      "env": {
        "FAL_KEY": "your-fal-api-key-here"
      }
    }
  }
}
```

配置后重启 Claude Desktop，在对话中就可以使用图片生成功能了。

## 2. 使用 FastMCP 客户端

### 基础使用
```python
import asyncio
from fastmcp import Client

async def main():
    # 连接到本地服务器
    async with Client("python fal_image_server.py") as client:
        # 生成图片
        result = await client.call_tool("generate_image", {
            "prompt": "一个宁静的日本花园，有樱花和锦鲤池",
            "model": "flux-dev",
            "num_images": 1,
            "image_size": "landscape_4_3"
        })
        
        print(result.text)

asyncio.run(main())
```

### 通过 HTTP 连接
如果你的服务器运行在 HTTP 模式：

```python
# 启动服务器时使用 HTTP transport
# python fal_image_server.py --transport http --port 8000

async with Client("http://localhost:8000/mcp") as client:
    # 使用相同的方式调用工具
    result = await client.call_tool("generate_image", {...})
```

## 3. 在 LangChain 中使用

```python
from langchain_mcp import MCPToolkit

# 创建 MCP 工具包
toolkit = MCPToolkit(
    server_command=["python", "/path/to/fal_image_server.py"],
    env={"FAL_KEY": "your-api-key"}
)

# 获取工具
tools = toolkit.get_tools()

# 在 LangChain agent 中使用
from langchain.agents import create_openai_tools_agent
agent = create_openai_tools_agent(llm, tools, prompt)
```

## 4. 使用 cURL 测试 (HTTP 模式)

当服务器以 HTTP 模式运行时：

```bash
# 列出可用工具
curl -X POST http://localhost:8000/mcp \
  -H "Content-Type: application/json" \
  -d '{
    "jsonrpc": "2.0",
    "method": "tools/list",
    "id": 1
  }'

# 调用生成图片工具
curl -X POST http://localhost:8000/mcp \
  -H "Content-Type: application/json" \
  -d '{
    "jsonrpc": "2.0",
    "method": "tools/call",
    "params": {
      "name": "generate_image",
      "arguments": {
        "prompt": "a beautiful sunset over mountains",
        "model": "flux-dev"
      }
    },
    "id": 2
  }'
```

## 5. 在 Claude 中的使用示例

配置好后，你可以在 Claude Desktop 中这样使用：

```
我：请帮我生成一张日式花园的图片

Claude：我来帮您生成一张日式花园的图片。

[调用 generate_image 工具]

已成功生成图片！这是一张展现宁静日式花园的图片，包含了樱花树和锦鲤池。
图片URL: [生成的图片链接]
```

## 6. 多服务器配置

你可以同时配置多个 MCP 服务器：

```json
{
  "mcpServers": {
    "fal-images": {
      "command": "python",
      "args": ["/path/to/fal_image_server.py"],
      "env": {"FAL_KEY": "your-key"}
    },
    "other-server": {
      "command": "node",
      "args": ["/path/to/other-server.js"]
    }
  }
}
```

## 7. 环境变量配置

除了在配置文件中设置，你也可以：

### 使用系统环境变量
```bash
# Linux/macOS
export FAL_KEY="your-api-key"
python fal_image_server.py

# Windows
set FAL_KEY=your-api-key
python fal_image_server.py
```

### 使用 .env 文件
创建 `.env` 文件：
```
FAL_KEY=your-api-key
```

然后修改服务器代码支持 dotenv：
```python
from dotenv import load_dotenv
load_dotenv()
```

## 故障排查

1. **服务器无法启动**
   - 检查 Python 版本是否为 3.10+
   - 确认已安装所有依赖：`pip install -r requirements.txt`

2. **API 调用失败**
   - 验证 FAL_KEY 是否正确设置
   - 检查网络连接

3. **Claude Desktop 无法连接**
   - 确认配置文件路径正确
   - 重启 Claude Desktop
   - 查看 Claude 的开发者工具日志

4. **权限问题**
   - 确保 Python 脚本有执行权限
   - Windows 用户可能需要调整防火墙设置