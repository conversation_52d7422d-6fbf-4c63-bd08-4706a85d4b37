# Fal.ai Image Generation MCP Server 🎨

A FastMCP server that provides image generation capabilities using [fal.ai](https://fal.ai)'s powerful AI models, including FLUX.
3588c888-d082-40df-8e87-a62d46c00b46:f345610a6a945aa828554143509dc3ca
## Features

- **Text-to-Image Generation**: Generate images from text prompts using FLUX models
- **Image-to-Image Transformation**: Transform existing images with text prompts
- **Multiple Models**: Support for FLUX Dev, Schnell, and Pro variants
- **Customizable Parameters**: Control image size, quality, and generation settings
- **Prompt Helper**: Built-in tool to create well-formatted prompts

## Prerequisites

- Python 3.10+
- A fal.ai API key (get one from [fal.ai dashboard](https://fal.ai/dashboard/keys))

## Installation

1. Install the required packages:

```bash
pip install -r requirements.txt
```

2. Set your fal.ai API key as an environment variable:

```bash
export FAL_KEY="your-api-key-here"
```

## Usage

### Running the Server

Start the MCP server:

```bash
python fal_image_server.py
```

Or use the FastMCP CLI:

```bash
fastmcp run fal_image_server.py
```

### Available Tools

#### 1. `generate_image`
Generate images from text prompts.

**Parameters:**
- `prompt` (required): Text description of the image
- `model`: Model to use (`flux-dev`, `flux-schnell`, `flux-pro`)
- `num_images`: Number of images to generate (1-4)
- `image_size`: Size preset (`square`, `landscape_4_3`, `portrait_4_3`, etc.)
- `seed`: Random seed for reproducibility
- `num_inference_steps`: Quality/speed tradeoff
- `guidance_scale`: How closely to follow the prompt

**Example:**
```python
result = await client.call_tool("generate_image", {
    "prompt": "a serene Japanese garden with cherry blossoms",
    "model": "flux-dev",
    "num_images": 2,
    "image_size": "landscape_4_3"
})
```

#### 2. `generate_image_from_image`
Transform existing images using text prompts.

**Parameters:**
- `image_url` (required): URL of the source image
- `prompt` (required): Description of the transformation
- `strength`: Transformation strength (0.0-1.0)
- `model`: Model to use (currently only `flux-dev`)
- `seed`: Random seed
- `num_inference_steps`: Quality setting

**Example:**
```python
result = await client.call_tool("generate_image_from_image", {
    "image_url": "https://example.com/photo.jpg",
    "prompt": "transform into a watercolor painting",
    "strength": 0.85
})
```

#### 3. `list_available_models`
Get information about available models.

#### 4. `get_image_size_options`
Get all available image size presets.

### Testing

Run the test script to verify everything is working:

```bash
python test_fal_server.py
```

### Integration with LLM Clients

This MCP server can be used with any MCP-compatible client:

**Claude Desktop:**
Add to your Claude configuration:

```json
{
  "mcpServers": {
    "fal-images": {
      "command": "python",
      "args": ["/path/to/fal_image_server.py"],
      "env": {
        "FAL_KEY": "your-api-key"
      }
    }
  }
}
```

**Using FastMCP Client:**
```python
from fastmcp import Client

async with Client("fal_image_server.py") as client:
    result = await client.call_tool("generate_image", {
        "prompt": "a futuristic city at sunset"
    })
```

## Model Information

- **FLUX.1 [dev]**: Balanced quality and speed, suitable for most use cases
- **FLUX.1 [schnell]**: Fast generation (1-4 steps), good for quick iterations
- **FLUX.1 [pro]**: Highest quality, professional use cases

## Tips for Best Results

1. **Be Descriptive**: Include details about style, lighting, composition, and mood
2. **Use Seeds**: Set a fixed seed when experimenting to get consistent results
3. **Adjust Steps**: More inference steps = better quality but slower generation
4. **Try Different Models**: Schnell for speed, Dev for balance, Pro for quality

## Error Handling

The server includes comprehensive error handling:
- Invalid API key errors
- Network timeouts
- Invalid parameters
- Model availability issues

All errors are returned in a structured format with helpful error messages.

## License

This MCP server is provided as-is for use with fal.ai's API. Please refer to fal.ai's terms of service for usage guidelines.