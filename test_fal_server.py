"""
Test script for the Fal.ai Image Generation MCP Server
"""
import asyncio
from fastmcp import Client


async def test_image_generation():
    """Test the image generation functionality"""
    
    # Connect to the MCP server (assuming it's running locally)
    async with Client("fal_image_server.py") as client:
        print("🔌 Connected to Fal.ai Image Generation Server")
        print()
        
        # List available tools
        tools = await client.list_tools()
        print("📋 Available tools:")
        for tool in tools:
            print(f"  - {tool.name}: {tool.description}")
        print()
        
        # Test 1: List available models
        print("🎨 Test 1: Listing available models...")
        models_result = await client.call_tool("list_available_models", {})
        print("Available models:")
        for model in models_result.text:
            print(f"  - {model['name']}: {model['description']}")
        print()
        
        # Test 2: Get image size options
        print("📐 Test 2: Getting image size options...")
        sizes_result = await client.call_tool("get_image_size_options", {})
        print("Available sizes:")
        for size in sizes_result.text:
            print(f"  - {size['size']}: {size['description']}")
        print()
        
        # Test 3: Generate an image
        print("🖼️  Test 3: Generating an image...")
        image_result = await client.call_tool("generate_image", {
            "prompt": "a serene Japanese garden with cherry blossoms and a koi pond",
            "model": "flux-dev",
            "num_images": 1,
            "image_size": "landscape_4_3",
            "seed": 42
        })
        
        result = image_result.text
        if result.get("success"):
            print("✅ Image generated successfully!")
            print(f"   Images: {len(result['images'])}")
            for i, img in enumerate(result['images']):
                print(f"   Image {i+1} URL: {img.get('url', 'N/A')}")
        else:
            print(f"❌ Error: {result.get('error')}")
        print()
        
        # Test 4: Use the prompt helper
        print("💡 Test 4: Using the prompt helper...")
        prompts = await client.list_prompts()
        if prompts:
            prompt_result = await client.execute_prompt("image_generation_prompt", {
                "subject": "a majestic mountain",
                "style": "oil painting",
                "mood": "dramatic",
                "details": "with storm clouds and lightning"
            })
            print(f"Generated prompt: {prompt_result.text}")


async def test_with_direct_server():
    """Test using direct server connection (no subprocess)"""
    from fal_image_server import mcp
    
    async with Client(mcp) as client:
        print("🔌 Connected to Fal.ai Image Generation Server (direct)")
        
        # Quick test
        result = await client.call_tool("list_available_models", {})
        print(f"✅ Direct connection test successful! Found {len(result.text)} models")


if __name__ == "__main__":
    print("🧪 Testing Fal.ai Image Generation MCP Server")
    print("=" * 50)
    print()
    
    # Note: Make sure FAL_KEY environment variable is set
    import os
    if not os.getenv("FAL_KEY"):
        print("⚠️  Warning: FAL_KEY environment variable not set")
        print("   The image generation will fail without it.")
        print("   Set it with: export FAL_KEY='your-api-key'")
        print()
    
    # Run tests
    asyncio.run(test_image_generation())
    
    print("\n" + "=" * 50)
    print("🧪 Testing direct server connection...")
    asyncio.run(test_with_direct_server())